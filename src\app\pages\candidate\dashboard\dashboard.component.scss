// Variables - Adaptées pour background assombri
$primary-color: #1976d2;
$secondary-color: rgba(255, 255, 255, 0.1);
$text-color: #ffffff; // Changé en blanc pour visibilité
$border-color: rgba(255, 255, 255, 0.3);
$shadow-color: rgba(0, 0, 0, 0.3);
$success-color: #4caf50;
$warning-color: #ff9800;
$error-color: #f44336;

// Mixins
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin card-shadow {
  box-shadow: 0 2px 4px $shadow-color;
  transition: box-shadow 0.3s ease;
  &:hover {
    box-shadow: 0 4px 8px $shadow-color;
  }
}

// Layout
.dashboard-container {
  min-height: 100vh;
  background-color: $secondary-color;
}

// Header candidat
.candidate-header {
  position: sticky;
  top: 0;
  z-index: 1000;

  .navbar {
    padding: 0.5rem 1rem;

    .navbar-brand {
      text-decoration: none;

      .logo-container {
        display: flex;
        align-items: center;

        .logo-image {
          height: 50px; // Taille réduite du logo
          width: auto;
          max-width: 150px;
        }
      }
    }

    .navbar-nav {
      .nav-item {
        color: white;
        text-decoration: none;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        border-radius: 6px;
        transition: all 0.3s ease;

        &:hover {
          background-color: transparent;
          color: violet;
        }

        mat-icon {
          font-size: 20px;
          width: 20px;
          height: 20px;
        }
      }
    }

    .profile-menu-container {
      .profile-button {
        border: none;
        background: none;
        cursor: pointer;
        border-radius: 50%;
        padding: 0.5rem;
        transition: background-color 0.3s ease;

        // &:hover {
        //   background-color: transparent;
        // }

        .profile-avatar {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          background-color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          color: black;

          mat-icon {
            font-size: 24px;
            width: 24px;
            height: 24px;
          }
        }
      }
    }
  }
}

// Contenu principal du dashboard
.dashboard-main {
  padding: 2rem 0;
  min-height: calc(100vh - 80px);

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
  }

  .welcome-section {
    text-align: center;
    margin-bottom: 3rem;
    padding: 2rem;
    background-color: transparent;
    // backdrop-filter: blur(10px);
    border-radius: 12px;
    // box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

    h1 {
      font-size: 2.5rem;
      color: white;
      margin-bottom: 1rem;
      font-weight: 600;
    }

    p {
      color: whitesmoke;
      font-size: 1.2rem;
      max-width: 600px;
      margin: 0 auto;
    }
  }

.search-section {
  margin-bottom: 3rem;

  .search-field {
    width: 100%;
    padding: 0.5rem;

    .mat-form-field-wrapper {
      padding-bottom: 0;
    }

    // Couleur du placeholder
    input::placeholder {
      color: rgba(255, 255, 255, 0.7) !important;
    }

    // Couleur des bordures
    .mat-form-field-outline {
      color: rgba(255, 255, 255, 0.5) !important;
    }
  }

  mat-hint {
    color: white;
  }
}

  // Stats section
  .stats-section {
    margin-bottom: 2rem;

    h2 {
      font-size: 1.5rem;
      color: $text-color;
      margin-bottom: 1.5rem;
    }

    app-candidate-stats {
      display: block;
      background-color: white;
      border-radius: 8px;
      @include card-shadow;
    }
  }

  // Filters section
  .filters-section {
    background-color: white;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    @include card-shadow;

    .search-container {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;

      mat-form-field {
        width: 100%;
      }

      .filter-actions {
        display: flex;
        gap: 1rem;
        align-items: flex-end;

        button {
          flex: 1;
        }
      }
    }
  }

  .offers-section {
    h2 {
      font-size: 2rem;
      color: white;
      margin-bottom: 2rem;
      font-weight: 600;
    }

    .job-list {
      display: flex;
      flex-direction: column;
      gap: 1.5rem;

      .job-card {
        background-color: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 12px;
        padding: 1.5rem;
        @include card-shadow;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        }

        .job-main-content {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 1rem;

          .job-info {
            flex: 1;

            .job-title {
              font-size: 1.4rem;
              font-weight: 600;
              color: #333; // Couleur sombre pour les cartes blanches
              margin-bottom: 0.5rem;
            }

            .job-details {
              .company {
                color: $primary-color;
                font-weight: 500;
                margin-bottom: 0.25rem;
              }

              .location-contract {
                color: #666; // Couleur grise pour les cartes blanches
                font-size: 0.9rem;

                .dot {
                  margin: 0 0.5rem;
                }
              }
            }
          }

          .view-button {
            background-color: $primary-color;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            transition: background-color 0.3s ease;

            &:hover {
              background-color: darken($primary-color, 10%);
            }
          }
        }

        .job-expanded-content {
          .divider {
            height: 1px;
            background-color: $border-color;
            margin: 1rem 0;
          }

          .additional-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;

            .detail-item {
              h4 {
                font-size: 0.9rem;
                font-weight: 600;
                color: $primary-color;
                margin-bottom: 0.25rem;
              }

              p {
                color: #333; // Couleur sombre pour les cartes blanches
                margin: 0;
              }
            }
          }

          .job-description,
          .job-requirements {
            margin-bottom: 1.5rem;

            h4 {
              font-size: 1.1rem;
              font-weight: 600;
              color: #333; // Couleur sombre pour les cartes blanches
              margin-bottom: 0.75rem;
            }

            p {
              color: #555; // Couleur grise pour les cartes blanches
              line-height: 1.6;
            }
          }

          .job-requirements {
            .competences-list {
              display: flex;
              flex-wrap: wrap;
              gap: 0.5rem;

              .competence-chip {
                background-color: rgba(25, 118, 210, 0.1);
                color: $primary-color;
                padding: 0.25rem 0.75rem;
                border-radius: 16px;
                font-size: 0.85rem;
                font-weight: 500;
              }
            }
          }

          .action-buttons {
            display: flex;
            gap: 1rem;
            margin-top: 1.5rem;

            .apply-button {
              background-color: $success-color;
              color: white;
              border: none;
              padding: 0.75rem 1.5rem;
              border-radius: 8px;
              cursor: pointer;
              font-weight: 500;
              display: flex;
              align-items: center;
              gap: 0.5rem;
              transition: background-color 0.3s ease;

              &:hover {
                background-color: darken($success-color, 10%);
              }

              mat-icon {
                font-size: 18px;
                width: 18px;
                height: 18px;
              }
            }

            .save-button {
              background-color: transparent;
              color: $primary-color;
              border: 2px solid $primary-color;
              padding: 0.75rem 1.5rem;
              border-radius: 8px;
              cursor: pointer;
              font-weight: 500;
              display: flex;
              align-items: center;
              gap: 0.5rem;
              transition: all 0.3s ease;

              &:hover {
                background-color: $primary-color;
                color: white;
              }

              mat-icon {
                font-size: 18px;
                width: 18px;
                height: 18px;
              }
            }
          }
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .candidate-header {
    .navbar {
      padding: 0.5rem;

      .navbar-brand {
        .logo-container {
          .logo-image {
            height: 40px; // Logo encore plus petit sur mobile
          }
        }
      }

      .navbar-nav {
        .nav-item {
          padding: 0.25rem 0.5rem;
          font-size: 0.9rem;

          mat-icon {
            font-size: 18px;
            width: 18px;
            height: 18px;
          }
        }
      }
    }
  }

  .dashboard-main {
    padding: 1rem 0;

    .container {
      padding: 0 0.5rem;
    }

    .welcome-section {
      padding: 1.5rem;
      margin-bottom: 2rem;

      h1 {
        font-size: 2rem;
      }

      p {
        font-size: 1rem;
      }
    }

    .offers-section {
      .job-list {
        .job-card {
          padding: 1rem;

          .job-main-content {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;

            .view-button {
              align-self: flex-end;
            }
          }

          .job-expanded-content {
            .additional-details {
              grid-template-columns: 1fr;
            }

            .action-buttons {
              flex-direction: column;

              .apply-button,
              .save-button {
                width: 100%;
                justify-content: center;
              }
            }
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .candidate-header {
    .navbar-nav {
      .nav-item {
        span {
          display: none; // Cache le texte, garde seulement les icônes
        }
      }
    }
  }

  .dashboard-main {
    .welcome-section {
      h1 {
        font-size: 1.8rem;
      }
    }
  }
}
.underline-hover::after {
  content: "";
  position: absolute;
  top: 2.5rem;
  bottom: 0;
  left: 0;
  width: 0%;
  height: 2px;
  background-color: white;
  transition: width 0.3s ease;
}

.underline-hover:hover::after {
  width: 100%;
}