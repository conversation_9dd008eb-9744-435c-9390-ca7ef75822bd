import { Component, OnInit, ViewChildren, QueryList } from '@angular/core';
import { FormBuilder, FormGroup, Validators, FormArray, AbstractControl } from '@angular/forms';
import { MatDatepicker } from '@angular/material/datepicker';
import { CandidatService, Candidat, ExperienceProfessionnelle, Formation, Langue } from '../../../services/candidat.service';
import { AlertsService } from '../../../services/alerts.service';
import { AuthService } from '../../../services/auth.service';

@Component({
  selector: 'app-profile',
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.scss']
})
export class ProfileComponent implements OnInit {
  @ViewChildren(MatDatepicker) datepickers!: QueryList<MatDatepicker<Date>>;
  
  profileForm: FormGroup;
  candidat: Candidat | null = null;
  isLoading = false;

  // Références pour les datepickers
  private expStartDatepickers: MatDatepicker<Date>[] = [];
  private expEndDatepickers: MatDatepicker<Date>[] = [];
  private formStartDatepickers: MatDatepicker<Date>[] = [];
  private formEndDatepickers: MatDatepicker<Date>[] = [];

  constructor(
    private fb: FormBuilder,
    private candidatService: CandidatService,
    private alertsService: AlertsService,
    private authService: AuthService
  ) {
    this.profileForm = this.fb.group({
      nom: ['', Validators.required],
      prenom: ['', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      telephone: [''],
      adresse: [''],
      dateNaissance: [''],
      genre: [''],
      nationalite: [''],
      photoUrl: [''],
      linkedinUrl: [''],
      githubUrl: [''],
      competences: this.fb.array([]),
      experiencesProfessionnelles: this.fb.array([]),
      formations: this.fb.array([]),
      langues: this.fb.array([])
    });
  }

  ngOnInit(): void {
    this.loadProfile();
  }

  // Méthodes pour gérer les références des datepickers
  getExpStartDatepicker(index: number): any {
    return `expStart_${index}`;
  }

  getExpEndDatepicker(index: number): any {
    return `expEnd_${index}`;
  }

  getFormStartDatepicker(index: number): any {
    return `formStart_${index}`;
  }

  getFormEndDatepicker(index: number): any {
    return `formEnd_${index}`;
  }

  loadProfile(): void {
    this.isLoading = true;
    const userId = this.authService.getCurrentUserId();
    
    if (userId) {
      this.candidatService.getCandidatProfile(userId).subscribe({
        next: (data) => {
          this.candidat = data;
          this.profileForm.patchValue(data);
          this.loadFormValues();
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Erreur lors du chargement du profil:', error);
          this.alertsService.showError('Erreur lors du chargement du profil');
          this.isLoading = false;
        }
      });
    } else {
      this.alertsService.showError('ID utilisateur non trouvé.');
      this.isLoading = false;
    }
  }

  loadFormValues(): void {
    if (this.candidat) {
      // Chargement des compétences
      this.setCompetences(this.candidat.competences || []);
      
      // Chargement des expériences professionnelles
      this.setExperiencesProfessionnelles(this.candidat.experiencesProfessionnelles || []);
      
      // Chargement des formations
      this.setFormations(this.candidat.formations || []);
      
      // Chargement des langues
      this.setLangues(this.candidat.langues || []);
    }
  }

  // Gestion des compétences
  get competences(): FormArray {
    return this.profileForm.get('competences') as FormArray;
  }

  addCompetenceInput(): void {
    this.competences.push(this.fb.control('', Validators.required));
  }

  removeCompetenceInput(index: number): void {
    this.competences.removeAt(index);
  }

  setCompetences(competences: string[]): void {
    this.competences.clear();
    competences.forEach(competence => {
      this.competences.push(this.fb.control(competence, Validators.required));
    });
  }

  // Gestion des expériences professionnelles
  get experiencesProfessionnelles(): FormArray {
    return this.profileForm.get('experiencesProfessionnelles') as FormArray;
  }

  addExperience(): void {
    const experienceGroup = this.fb.group({
      titre: ['', Validators.required],
      entreprise: ['', Validators.required],
      ville: ['', Validators.required],
      dateDebut: ['', Validators.required],
      dateFin: [''],
      description: ['']
    });
    this.experiencesProfessionnelles.push(experienceGroup);
  }

  removeExperience(index: number): void {
    this.experiencesProfessionnelles.removeAt(index);
  }

  setExperiencesProfessionnelles(experiences: ExperienceProfessionnelle[]): void {
    this.experiencesProfessionnelles.clear();
    experiences.forEach(experience => {
      this.experiencesProfessionnelles.push(this.fb.group({
        titre: [experience.titre, Validators.required],
        entreprise: [experience.entreprise, Validators.required],
        ville: [experience.ville, Validators.required],
        dateDebut: [experience.dateDebut, Validators.required],
        dateFin: [experience.dateFin],
        description: [experience.description]
      }));
    });
  }

  // Gestion des formations
  get formations(): FormArray {
    return this.profileForm.get('formations') as FormArray;
  }

  addFormation(): void {
    const formationGroup = this.fb.group({
      titreDiplome: ['', Validators.required],
      etablissement: ['', Validators.required],
      ville: ['', Validators.required],
      dateDebut: ['', Validators.required],
      dateFin: ['', Validators.required],
      description: ['']
    });
    this.formations.push(formationGroup);
  }

  removeFormation(index: number): void {
    this.formations.removeAt(index);
  }

  setFormations(formations: Formation[]): void {
    this.formations.clear();
    formations.forEach(formation => {
      this.formations.push(this.fb.group({
        titreDiplome: [formation.titreDiplome, Validators.required],
        etablissement: [formation.etablissement, Validators.required],
        ville: [formation.ville, Validators.required],
        dateDebut: [formation.dateDebut, Validators.required],
        dateFin: [formation.dateFin, Validators.required],
        description: [formation.description]
      }));
    });
  }

  // Gestion des langues
  get langues(): FormArray {
    return this.profileForm.get('langues') as FormArray;
  }

  addLangue(): void {
    const langueGroup = this.fb.group({
      nom: ['', Validators.required],
      niveau: ['', Validators.required]
    });
    this.langues.push(langueGroup);
  }

  removeLangue(index: number): void {
    this.langues.removeAt(index);
  }

  setLangues(langues: Langue[]): void {
    this.langues.clear();
    langues.forEach(langue => {
      this.langues.push(this.fb.group({
        nom: [langue.nom, Validators.required],
        niveau: [langue.niveau, Validators.required]
      }));
    });
  }

  // Helper pour convertir AbstractControl en FormGroup
  asFormGroup(control: AbstractControl): FormGroup {
    return control as FormGroup;
  }

  // Gestion de l'upload de photo
  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files[0]) {
      const file = input.files[0];
      // Ici vous devriez implémenter la logique pour uploader le fichier
      // et mettre à jour photoUrl avec l'URL retournée
      const reader = new FileReader();
      reader.onload = () => {
        this.profileForm.patchValue({ photoUrl: reader.result as string });
      };
      reader.readAsDataURL(file);
    }
  }

  // Sauvegarde du profil
  saveProfile(): void {
    if (this.profileForm.invalid) {
      this.alertsService.showError('Veuillez corriger les erreurs dans le formulaire');
      return;
    }

    this.isLoading = true;
    const userId = this.authService.getCurrentUserId();
    
    if (userId) {
      this.candidatService.updateCandidatProfile(userId, this.profileForm.value).subscribe({
        next: () => {
          this.alertsService.showSuccess('Profil mis à jour avec succès');
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Erreur lors de la mise à jour du profil:', error);
          this.alertsService.showError('Erreur lors de la mise à jour du profil');
          this.isLoading = false;
        }
      });
    } else {
      this.alertsService.showError('ID utilisateur non trouvé.');
      this.isLoading = false;
    }
  }
}