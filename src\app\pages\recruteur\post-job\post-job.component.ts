import { Component, OnDestroy } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { OffreService } from 'src/app/services/offre.service';
import Swal from 'sweetalert2';


@Component({
  selector: 'app-post-job',
  templateUrl: './post-job.component.html',
  styleUrls: ['./post-job.component.scss']
})
export class PostJobComponent implements OnDestroy {
  jobForm!: FormGroup;
  isEditMode = false; // Mode édition ou création
  currentOffreId: number | null = null; // ID de l'offre en cours d'édition

  
    // Options pour le type de domaine
  domaine = [
    { value: 'Developpeur FULL-STACK', label: 'Developpeur FULL-STACK' },
    { value: 'Developpeur BACK-END', label: 'Developpeur BACK-END' },
    { value: 'Developpeur FRONT-END', label: 'Developpeur FRONT-END' },
    { value: 'Comptabilité', label: 'Comptabilité' },
    { value: 'Professeur en JAVA', label: 'Professeur en JAVA' },
    { value: 'Désigner UI / UX', label: 'Désigner UI / UX' },
    { value: 'Cuisinier', label: 'Cuisinier' },
    { value: 'Les Ressources Humaines', label: 'Les Ressources Humaines' }
  ];
  
  // Options pour le type de contrat
  contractTypes = [
    { value: 'cdi', label: 'CDI' },
    { value: 'cdd', label: 'CDD' },
    { value: 'ctt', label: 'CTT' },
    { value: 'interim', label: 'Intérim' },
    { value: 'freelance', label: 'Freelance' },
    { value: 'stage', label: 'Stage' },
    { value: 'alternance', label: 'Alternance' }
  ];

  
  // Options pour le niveau d'expérience
  experienceLevels = [
    { value: 'debutant', label: 'Débutant' },
    { value: 'junior', label: 'Junior (1-3 ans)' },
    { value: 'intermediaire', label: 'Intermédiaire (3-5 ans)' },
    { value: 'senior', label: 'Senior (5-10 ans)' },
    { value: 'expert', label: 'Expert (10+ ans)' }
  ];

  constructor(
    private fb: FormBuilder,
    private titleService: Title,
    private router: Router,
    private offreService: OffreService
  ) { }

  ngOnInit(): void {
    this.initForm();
    this.checkEditMode(); // Vérifier si on est en mode édition
    this.titleService.setTitle(this.isEditMode ? 'Modifier une offre' : 'Ajouter une offre');
  }

  initForm(): void {
    this.jobForm = this.fb.group({
      title: ['', [Validators.required]],
      domaine: ['', [Validators.required]],
      salary: ['', [Validators.required]],
      contractType: ['', [Validators.required]],
      city: ['', [Validators.required]],
      experienceLevel: ['', [Validators.required]],
      skills: ['', [Validators.required]],
      description: ['', [Validators.required]]
    });
  }

  onSubmit(): void {
    if (this.jobForm.valid) {
      console.log('Offre soumise:', this.jobForm.value);
      // Ici vous pourriez appeler un service pour envoyer les données au backend
    } else {
      this.markFormGroupTouched(this.jobForm);
    }
  }

  // Marquer tous les champs comme touchés pour afficher les erreurs
  markFormGroupTouched(formGroup: FormGroup): void {
    Object.values(formGroup.controls).forEach(control => {
      control.markAsTouched();
      if ((control as FormGroup).controls) {
        this.markFormGroupTouched(control as FormGroup);
      }
    });
  }

  confirmerPublication() {
  const swalWithBootstrapButtons = Swal.mixin({
    customClass: {
      confirmButton: "btn btn-success",
      cancelButton: "btn btn-danger me-2"
    },
    buttonsStyling: false
  });

  const isEdit = this.isEditMode;

  swalWithBootstrapButtons.fire({
    title: "Êtes-vous sûr ?",
    text: isEdit
      ? "Cette action modifiera l'offre existante !"
      : "Cette action publiera l'offre, et vous n'avez pas le droit pour la modifier !",
    icon: "warning",
    showCancelButton: true,
    confirmButtonText: isEdit ? "Oui, modifier" : "Oui, publier",
    cancelButtonText: "Annuler",
    reverseButtons: true
  }).then((result) => {
    if (result.isConfirmed) {
      // 👉 Appelle ici la fonction pour publier l’offre
      this.publierOffre(); // Tu dois définir cette fonction toi-même
      swalWithBootstrapButtons.fire({
        title: isEdit ? "Modifié !" : "Publié !",
        text: "L’offre a été publiée avec succès.",
        icon: "success"
      });
    } else if (
      result.dismiss === Swal.DismissReason.cancel
    ) {
      swalWithBootstrapButtons.fire({
        title: "Annulé",
        text: isEdit
          ? "La modification a été annulée."
          : "La publication a été annulée.",
        icon: "error"
      });
    }
  });
}

publierOffre() {
  if (!this.jobForm.valid) {
    console.error('Formulaire invalide');
    return;
  }

  const offreData = this.jobForm.value;
  console.log('Données de l\'offre à publier:', offreData);

  if (this.isEditMode && this.currentOffreId) {
    // Mode modification
    this.offreService.updateOffre(this.currentOffreId, offreData)
      .subscribe({
        next: (offreModifiee) => {
          console.log('Offre modifiée avec succès:', offreModifiee);
          this.offreService.clearOffreToEdit();
          this.router.navigate(['/recruteur/dashboard']);
        },
        error: (error) => {
          console.error('Erreur lors de la modification (API):', error);
          // Fallback : modification locale
          this.modifierOffreLocalement(offreData);
        }
      });
  } else {
    // Mode création
    this.offreService.createOffre(offreData)
      .subscribe({
        next: (nouvelleOffre) => {
          console.log('Offre créée avec succès:', nouvelleOffre);
          this.offreService.clearOffreToEdit();
          this.router.navigate(['/recruteur/dashboard']);
        },
        error: (error) => {
          console.error('Erreur lors de la création (API):', error);
          // Fallback : création locale
          this.creerOffreLocalement(offreData);
        }
      });
  }
}

// Méthode pour créer une offre localement (fallback)
private creerOffreLocalement(offreData: any): void {
  console.log('Création locale de l\'offre...');
  const nouvelleOffre = this.offreService.createOffreLocally(offreData);
  console.log('Offre créée localement:', nouvelleOffre);

  this.offreService.clearOffreToEdit();
  this.router.navigate(['/recruteur/dashboard']);
}

// Méthode pour modifier une offre localement (fallback)
private modifierOffreLocalement(offreData: any): void {
  console.log('Modification locale de l\'offre...');
  // Simuler la modification locale
  const offresActuelles = this.offreService.getCurrentOffresList();
  const index = offresActuelles.findIndex(o => o.id === this.currentOffreId);

  if (index !== -1) {
    const offreModifiee = {
      ...offresActuelles[index],
      titre: offreData.title,
      description: offreData.description,
      domaine: offreData.domaine,
      ville: offreData.city,
      typeContrat: offreData.contractType,
      salaire: offreData.salary,
      competences: offreData.skills ? offreData.skills.split(',').map((s: string) => s.trim()) : []
    };

    this.offreService.updateOffreInList(offreModifiee);
    console.log('Offre modifiée localement:', offreModifiee);
  }

  this.offreService.clearOffreToEdit();
  this.router.navigate(['/recruteur/dashboard']);
}

// Méthode pour vérifier si on est en mode édition
// TODO: Intégrer avec l'API backend pour récupérer les données complètes de l'offre
checkEditMode(): void {
  // Vérifier s'il y a des données d'offre à éditer dans le service
  const offreToEdit = this.offreService.getOffreToEdit();
  if (offreToEdit) {
    this.isEditMode = true;
    this.currentOffreId = offreToEdit.id;
    this.populateForm(offreToEdit);
    console.log('Mode édition activé avec les données:', offreToEdit);
  } else {
    this.isEditMode = false;
    this.currentOffreId = null;
    console.log('Mode création activé');
  }
}

// Méthode pour pré-remplir le formulaire avec les données de l'offre à modifier
populateForm(offreData: any): void {
  if (!this.jobForm) {
    console.error('Le formulaire n\'est pas encore initialisé');
    return;
  }

  console.log('Pré-remplissage du formulaire avec:', offreData);

  this.jobForm.patchValue({
    title: offreData.titre,
    domaine: offreData.domaine,
    salary: offreData.salaire,
    contractType: offreData.contractType,
    city: offreData.ville,
    experienceLevel: offreData.experienceLevel,
    skills: offreData.skills,
    description: offreData.description
  });

  console.log('Formulaire après pré-remplissage:', this.jobForm.value);
}

// Nettoyer les données d'édition quand on quitte le composant
ngOnDestroy(): void {
  // Ne pas nettoyer si on est en train de publier (redirection vers dashboard)
  // Les données seront nettoyées dans publierOffre()
}

}
