<header class="sticky-header">
  <div class="container-fluid">
    <nav class="navbar navbar-expand-lg">
      <!-- Logo à gauche -->
      <a class="navbar-brand">
        <div class="logo-container">
          <img src="assets/images/loogo.png" alt="RecruitIQ Logo" class="logo-image">
        </div>
      </a>

      <!-- Boutons à droite -->
      <div class="navbar-nav ms-auto d-flex flex-row align-items-center">
        <!-- Lien Accueil -->
        <a mat-button routerLink="/" class="nav-item me-3 underline-hover color">
          Accueil
        </a>
        <!-- Bouton profil -->
      </div>
    </nav>
  </div>
</header>

<!-- État de la connexion -->
<div class="connection-status" [class.offline]="!isOnline">
  <mat-icon>{{ isOnline ? 'wifi' : 'wifi_off' }}</mat-icon>
  <span>{{ isOnline ? 'Connecté' : 'Hors ligne' }}</span>
</div>

<!-- Message d'erreur -->
<div class="error-message" *ngIf="errorMessage">
  <mat-icon>error</mat-icon>
  <span>{{ errorMessage }}</span>
</div>

<!-- ---------------------------------------------------------- -->

<div class="entretien-container">
  <!-- Étape 1 - Accueil -->
  <div *ngIf="etape === 1" class="etape etape-accueil">
    <h1>Bonjour Mr. 
      <strong>{{ candidat.nom }} {{ candidat.prenom }}</strong><br>
      à votre entretien pour le poste de <strong>{{ candidat.Intitul_Offre }}</strong> chez <strong>{{ candidat.Nom_Entreprise}}</strong>.<br>
      Votre parcours en <strong>{{ candidat.Domaine }}</strong> a particulièrement retenu notre attention.<br>
      Nous vous invitons à démontrer vos compétences et votre motivation.<br>
      Bonne chance !
    </h1>
    <button (click)="commencerEntretien()" class="btn btn-primary" [disabled]="!isOnline || isProcessing">
      Commencer l'entretien
    </button>
  </div>

  <!-- Étape 2 - Conditions -->
  <div *ngIf="etape === 2" class="etape etape-conditions">
    <h2>Conditions de l'entretien</h2>
    
    <div class="conditions-list">
      <ul>
        <li *ngFor="let condition of conditions">{{ condition }}</li>
      </ul>
    </div>
    
    <div class="confirmation-checkbox">
      <input 
        type="checkbox" 
        id="confirmation" 
        [(ngModel)]="confirme"
        class="green-checkbox"
        [disabled]="!isOnline"
      >
      <label for="confirmation" class="green-label">J'ai lu et j'accepte les conditions</label>
    </div>
    
    <button 
      (click)="confirmerConditions()" 
      [disabled]="!confirme || !isOnline || isProcessing"
      class="btn btn-primary"
    >
      Commencer l'entretien
    </button>
  </div>

  <!-- Étape 3 - Entretien -->
  <div *ngIf="etape === 3" class="etape etape-entretien">
    <div class="entretien-container">
      <div class="progress-indicator">
        <span class="question-counter">Question {{ currentQuestionIndex + 1 }}/{{ totalQuestions }}</span>
        <mat-progress-bar 
          mode="determinate" 
          [value]="(currentQuestionIndex / totalQuestions) * 100"
          color="primary">
        </mat-progress-bar>
      </div>

      <h2>Entretien en cours</h2>

      <!-- Bloc Question IA -->
      <div class="ai-block" [class.active]="aiSpeaking" [class.error]="!isOnline">
        <div class="ai-mic-icon">
          <svg width="44" height="44" viewBox="0 0 24 24" fill="none">
            <circle cx="12" cy="12" r="10" fill="#fff"/>
            <path d="M12 17a5 5 0 0 0 5-5V9a5 5 0 0 0-10 0v3a5 5 0 0 0 5 5zm-7-5h2m10 0h2M12 17v4m-3 0h6"
                  stroke="#ab47bc" stroke-width="2" stroke-linecap="round"/>
          </svg>
          <span class="ai-wave" *ngIf="aiSpeaking"></span>
        </div>
        <div class="ai-question-text">
          {{ aiQuestion }}
        </div>
        <div class="ai-status" *ngIf="aiSpeaking">
          <mat-icon>record_voice_over</mat-icon>
          <span>L'IA pose la question...</span>
        </div>
      </div>

      <!-- Bloc Réponse Candidat -->
      <div class="candidat-block" [class.active]="isListening" [class.error]="!isOnline">
        <div class="mic-icon">
          <svg width="44" height="44" viewBox="0 0 24 24" fill="none">
            <circle cx="12" cy="12" r="10" fill="#fff"/>
            <path d="M12 16a4 4 0 0 0 4-4V9a4 4 0 1 0-8 0v3a4 4 0 0 0 4 4zm-7-4h2m10 0h2M12 16v4m-3 0h6"
                  stroke="#6a0572" stroke-width="2" stroke-linecap="round"/>
          </svg>
          <span class="wave" *ngIf="isListening"></span>
        </div>
        <div class="recording-controls">
          <button 
            (click)="toggleListening()" 
            [disabled]="aiSpeaking || !isOnline || isProcessing" 
            class="voice-control-btn"
            [class.recording]="isListening"
          >
            <span>
              {{ isListening ? 'Arrêter (' + remainingTime + 's)' : 'Commencer (30s)' }}
            </span>
            <span class="pulse-circle" *ngIf="isListening"></span>
          </button>
          <div class="recording-status" *ngIf="isListening">
            <mat-icon>mic</mat-icon>
            <span>Enregistrement en cours...</span>
          </div>
        </div>
      </div>

      <!-- Affichage transcription -->
      <div class="transcript-container">
        <h3>Vos réponses</h3>
        <div class="transcript-content" [class.empty]="!transcript">
          <p *ngIf="transcript && !isListening; else emptyState">{{ transcript }}</p>
          <ng-template #emptyState>
            <div class="empty-state">
              <p>{{ isListening ? 'Enregistrement en cours...' : 'Votre réponse apparaîtra ici...' }}</p>
            </div>
          </ng-template>
        </div>
      </div>

      <!-- Boutons de contrôle -->
      <div class="control-buttons">
        <button 
          (click)="submitAnswer()" 
          [disabled]="!transcript || isListening || !isOnline || isProcessing"
          class="btn btn-primary submit-btn"
        >
          <mat-icon>send</mat-icon>
          {{ currentQuestionIndex < totalQuestions - 1 ? 'Question suivante' : 'Terminer l\'entretien' }}
        </button>
      </div>

      <!-- Indicateur de chargement -->
      <div class="loading-overlay" *ngIf="isProcessing">
        <mat-spinner diameter="40"></mat-spinner>
        <span>Traitement en cours...</span>
      </div>
    </div>
  </div>
</div>