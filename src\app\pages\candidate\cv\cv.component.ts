import { Component, OnInit } from '@angular/core';
import { CvService, CvInfo } from '../../../services/cv.service';
import { AlertsService } from '../../../services/alerts.service';
import { AuthService } from '../../../services/auth.service';

@Component({
  selector: 'app-cv',
  templateUrl: './cv.component.html',
  styleUrls: ['./cv.component.scss']
})
export class CvComponent implements OnInit {
  currentCv: CvInfo | null = null;
  selectedFile: File | null = null;
  isLoading = false;

  constructor(
    private cvService: CvService,
    private alertsService: AlertsService,
    private authService: AuthService
  ) { }

  ngOnInit(): void {
    this.loadCvInfo();
  }

  loadCvInfo(): void {
    this.isLoading = true;
    const userId = this.authService.getCurrentUserId();
    if (userId) {
      this.cvService.getCvInfo(userId).subscribe({
        next: (cvs) => {
          // Assuming we only care about the most recent CV for now
          if (cvs && cvs.length > 0) {
            this.currentCv = cvs[0];
          }
          this.isLoading = false;
        },
        error: (error) => {
          this.alertsService.showError('Erreur lors du chargement du CV');
          this.isLoading = false;
        }
      });
    } else {
      this.alertsService.showError('ID utilisateur non trouvé.');
      this.isLoading = false;
    }
  }

  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files[0]) {
      const file = input.files[0];

      // Basic validation
      const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
      const maxSizeMb = 5;

      if (!allowedTypes.includes(file.type)) {
        this.alertsService.showError('Format de fichier non supporté. Veuillez télécharger un PDF, DOC ou DOCX.');
        this.selectedFile = null;
        return;
      }

      if (file.size > maxSizeMb * 1024 * 1024) {
        this.alertsService.showError(`La taille du fichier ne doit pas dépasser ${maxSizeMb} Mo.`);
        this.selectedFile = null;
        return;
      }

      this.selectedFile = file;
      this.alertsService.showInfo(`Fichier sélectionné: ${file.name}`);
    }
  }

  uploadCv(): void {
    if (!this.selectedFile) {
      this.alertsService.showWarning('Veuillez sélectionner un fichier CV à télécharger.');
      return;
    }

    this.isLoading = true;
    this.cvService.uploadCv(this.selectedFile).subscribe({
      next: (cvInfo) => {
        this.alertsService.showSuccess('CV téléchargé avec succès et analysé par l\'IA !');
        this.currentCv = cvInfo;
        this.selectedFile = null; // Clear selected file after successful upload
        this.isLoading = false;
      },
      error: (error) => {
        this.alertsService.showError('Erreur lors du téléchargement du CV.');
        this.isLoading = false;
      }
    });
  }

  deleteCv(): void {
    if (!this.currentCv) return;

    this.alertsService.showConfirm(
      'Confirmation',
      'Êtes-vous sûr de vouloir supprimer votre CV ?',
      'Supprimer',
      'Annuler'
    ).then((confirmed) => {
      if (confirmed && this.currentCv) {
        this.isLoading = true;
        this.cvService.deleteCv(this.currentCv.id).subscribe({
          next: () => {
            this.alertsService.showSuccess('CV supprimé avec succès.');
            this.currentCv = null;
            this.isLoading = false;
          },
          error: (error) => {
            this.alertsService.showError('Erreur lors de la suppression du CV.');
            this.isLoading = false;
          }
        });
      }
    });
  }

  downloadCv(): void {
    if (this.currentCv && this.currentCv.cheminFichier) {
      // This assumes cheminFichier is a direct URL to the downloadable file
      // For security, a backend endpoint to serve the file might be better
      window.open(this.currentCv.cheminFichier, '_blank');
    } else {
      this.alertsService.showWarning('Aucun CV disponible à télécharger.');
    }
  }
} 