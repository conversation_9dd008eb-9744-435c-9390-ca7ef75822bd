import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { OffreService, Offre } from '../../../services/offre.service';
import { PostulationService } from '../../../services/postulation.service';
import { AuthService } from '../../../services/auth.service';
import { PostulationRequest } from '../../../models/postulation.model';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import Swal from 'sweetalert2';
import { FormBuilder, FormGroup } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { AlertsService } from '../../../services/alerts.service';
import { PostulationDialogComponent } from '../../../components/postulation-dialog/postulation-dialog.component';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  offres: Offre[] = [];
  filteredOffres: Offre[] = [];
  searchText: string = '';
  isProfileMenuOpen = false;
  currentUserId: number | null = null;
  currentUser: any;
  isLoading = false;
  isFiltering = false;
  filterForm: FormGroup;
  mesPostulations: any[] = [];
  postulationForm: FormGroup;

  constructor(
    private titleService: Title,
    private router: Router,
    private offreService: OffreService,
    private postulationService: PostulationService,
    private authService: AuthService,
    private alertsService: AlertsService,
    private dialog: MatDialog,
    private fb: FormBuilder
  ) {
    this.currentUserId = this.authService.getCurrentUserId();
    this.filterForm = this.fb.group({
      search: [''],
      domaine: [''],
      ville: [''],
      typeContrat: ['']
    });
    this.postulationForm = this.fb.group({
      cv: ['']
    });
  }

  ngOnInit() {
    this.titleService.setTitle('Tableau de bord candidat');
    this.loadCurrentUser();
    // this.loadOffres(); // Commenté pour utiliser les données statiques
    this.loadStaticOffres(); // Utiliser les données statiques
    this.loadMesPostulations();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private loadCurrentUser() {
    this.authService.currentUser$
      .pipe(takeUntil(this.destroy$))
      .subscribe((user: any) => {
        if (user) {
          this.currentUserId = user.id;
          this.currentUser = user;
        }
      });
  }

  private loadOffres() {
    this.isLoading = true;
    this.offreService.getOffres()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (offres: Offre[]) => {
          this.offres = offres;
          this.filteredOffres = offres;
          this.isLoading = false;
        },
        error: (error: any) => {
          console.error('Erreur lors du chargement des offres:', error);
          Swal.fire({
            icon: 'error',
            title: 'Erreur',
            text: 'Impossible de charger les offres d\'emploi. Veuillez réessayer plus tard.'
          });
          this.isLoading = false;
        }
      });
  }

  private loadMesPostulations(): void {
    // this.postulationService.getMesPostulations().subscribe({
    //   next: (postulations) => {
    //     this.mesPostulations = postulations;
    //   },
    //   error: (error) => {
    //     this.alertsService.showError('Erreur lors du chargement de vos postulations');
    //   }
    // });

    // Données statiques pour les postulations
    this.mesPostulations = [
      {
        id: 1,
        offreId: 1,
        titre: 'Développeur Full Stack Angular/Spring Boot',
        entreprise: 'TechCorp',
        datePostulation: new Date('2024-01-15'),
        statut: 'EN_COURS'
      },
      {
        id: 2,
        offreId: 3,
        titre: 'Chef de Projet Digital',
        entreprise: 'DigitalSolutions',
        datePostulation: new Date('2024-01-10'),
        statut: 'ACCEPTEE'
      }
    ];
  }

  applyFilter() {
    if (!this.searchText) {
      this.filteredOffres = this.offres;
      return;
    }

    const searchLower = this.searchText.toLowerCase();
    this.filteredOffres = this.offres.filter(offre => 
      offre.titre.toLowerCase().includes(searchLower) ||
      offre.description.toLowerCase().includes(searchLower) ||
      offre.domaine.toLowerCase().includes(searchLower) ||
      offre.ville.toLowerCase().includes(searchLower) ||
      offre.typeContrat.toLowerCase().includes(searchLower) ||
      offre.competences.some((comp: string) => comp.toLowerCase().includes(searchLower))
    );
  }

  toggleProfileMenu() {
    this.isProfileMenuOpen = !this.isProfileMenuOpen;
  }

  async postuler(offre: Offre) {
    if (!this.currentUserId) {
      Swal.fire({
        icon: 'error',
        title: 'Erreur',
        text: 'Vous devez être connecté pour postuler.'
      });
      return;
    }

    if (this.postulationForm.valid) {
      const cvFile = this.postulationForm.get('cv')?.value;
      const { value: lettreMotivation } = await Swal.fire({
        title: 'Lettre de motivation',
        input: 'textarea',
        inputLabel: 'Veuillez rédiger votre lettre de motivation',
        inputPlaceholder: 'Écrivez votre lettre de motivation ici...',
        showCancelButton: true,
        inputValidator: (value) => {
          if (!value) {
            return 'La lettre de motivation est obligatoire';
          }
          return null;
        }
      });

      if (lettreMotivation) {
        const formData = new FormData();
        formData.append('offreId', offre.id.toString());
        formData.append('lettreMotivation', lettreMotivation);
        formData.append('cv', cvFile);

        this.postulationService.postuler({
          offreId: offre.id,
          lettreMotivation,
          cv: cvFile
        }).subscribe({
          next: (response) => {
            Swal.fire({
              icon: 'success',
              title: 'Candidature envoyée !',
              text: 'Votre candidature a été envoyée avec succès. Vous recevrez bientôt une réponse.',
              timer: 3000,
              showConfirmButton: false
            });
          },
          error: (error) => {
            console.error('Erreur lors de la postulation:', error);
            Swal.fire({
              icon: 'error',
              title: 'Erreur',
              text: 'Une erreur est survenue lors de l\'envoi de votre candidature. Veuillez réessayer.'
            });
          }
        });
      }
    }
  }

  sauvegarderOffre(offre: Offre) {
    // TODO: Implémenter la sauvegarde des offres
    Swal.fire({
      icon: 'info',
      title: 'Fonctionnalité à venir',
      text: 'La sauvegarde des offres sera bientôt disponible.'
    });
  }

  applyFilters(): void {
    this.isFiltering = true;
    const filter = this.filterForm.value;
    this.offreService.getOffres(filter).subscribe({
      next: (offres) => {
        this.offres = offres;
        this.isFiltering = false;
      },
      error: (error) => {
        this.alertsService.showError('Erreur lors de l\'application des filtres');
        this.isFiltering = false;
      }
    });
  }

  resetFilters(): void {
    this.filterForm.reset();
    this.loadOffres();
  }

  postulerDialog(offre: Offre) {
    const dialogRef = this.dialog.open(PostulationDialogComponent, {
      width: '500px',
      data: { offreId: offre.id }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadMesPostulations();
      }
    });
  }

  deconnexion(): void {
    this.authService.logout();
  }

  logout(): void {
    this.authService.logout();
  }

  // Méthode pour charger des données statiques de démonstration
  private loadStaticOffres(): void {
    this.isLoading = true;

    // Simulation d'un délai de chargement
    setTimeout(() => {
      this.offres = [
        {
          id: 1,
          titre: 'Développeur Full Stack Angular/Spring Boot',
          description: 'Nous recherchons un développeur full stack expérimenté pour rejoindre notre équipe dynamique. Vous travaillerez sur des projets innovants utilisant Angular pour le frontend et Spring Boot pour le backend. Une excellente opportunité de développer vos compétences dans un environnement collaboratif.',
          domaine: 'Informatique',
          ville: 'Rabat',
          typeContrat: 'CDI',
          salaire: 45000,
          heuresParSemaine: 40,
          dateLimite: new Date('2024-02-15'),
          competences: ['Angular', 'Spring Boot', 'TypeScript', 'Java', 'PostgreSQL', 'Git'],
          recruteurId: 101,
          expanded: false
        },
        {
          id: 2,
          titre: 'Designer UX/UI Senior',
          description: 'Rejoignez notre équipe créative en tant que Designer UX/UI Senior. Vous serez responsable de la conception d\'interfaces utilisateur intuitives et esthétiques pour nos applications web et mobiles. Expérience avec Figma et Adobe Creative Suite requise.',
          domaine: 'Design',
          ville: 'Casablanca',
          typeContrat: 'CDI',
          salaire: 38000,
          heuresParSemaine: 35,
          dateLimite: new Date('2024-02-20'),
          competences: ['Figma', 'Adobe XD', 'Photoshop', 'Illustrator', 'Prototypage', 'Design System'],
          recruteurId: 102,
          expanded: false
        },
        {
          id: 3,
          titre: 'Chef de Projet Digital',
          description: 'Nous cherchons un Chef de Projet Digital pour piloter nos projets de transformation numérique. Vous coordonnerez les équipes techniques et métier, gérerez les budgets et assurerez la livraison dans les délais. Certification PMP appréciée.',
          domaine: 'Management',
          ville: 'Marrakech',
          typeContrat: 'CDI',
          salaire: 55000,
          heuresParSemaine: 40,
          dateLimite: new Date('2024-02-25'),
          competences: ['Gestion de projet', 'Agile', 'Scrum', 'Leadership', 'Budget', 'Communication'],
          recruteurId: 103,
          expanded: false
        },
        {
          id: 4,
          titre: 'Développeur Mobile React Native',
          description: 'Opportunité passionnante pour un développeur mobile spécialisé en React Native. Vous développerez des applications mobiles cross-platform pour iOS et Android. Expérience avec les API REST et les services cloud requise.',
          domaine: 'Informatique',
          ville: 'Fès',
          typeContrat: 'CDD',
          salaire: 35000,
          heuresParSemaine: 40,
          dateLimite: new Date('2024-03-01'),
          competences: ['React Native', 'JavaScript', 'Redux', 'API REST', 'Firebase', 'Git'],
          recruteurId: 104,
          expanded: false
        },
        {
          id: 5,
          titre: 'Analyste Cybersécurité',
          description: 'Rejoignez notre équipe de sécurité informatique en tant qu\'Analyste Cybersécurité. Vous serez responsable de la surveillance des menaces, de l\'analyse des incidents de sécurité et de la mise en place de mesures préventives.',
          domaine: 'Sécurité',
          ville: 'Rabat',
          typeContrat: 'CDI',
          salaire: 48000,
          heuresParSemaine: 40,
          dateLimite: new Date('2024-03-05'),
          competences: ['Cybersécurité', 'SIEM', 'Analyse forensique', 'ISO 27001', 'Firewall', 'Penetration Testing'],
          recruteurId: 105,
          expanded: false
        },
        {
          id: 6,
          titre: 'Data Scientist',
          description: 'Nous recherchons un Data Scientist pour analyser nos données et développer des modèles prédictifs. Vous travaillerez avec Python, R et des outils de machine learning pour extraire des insights précieux de nos données.',
          domaine: 'Data Science',
          ville: 'Casablanca',
          typeContrat: 'CDI',
          salaire: 52000,
          heuresParSemaine: 40,
          dateLimite: new Date('2024-03-10'),
          competences: ['Python', 'R', 'Machine Learning', 'SQL', 'Pandas', 'TensorFlow', 'Jupyter'],
          recruteurId: 106,
          expanded: false
        }
      ];

      this.filteredOffres = [...this.offres];
      this.isLoading = false;
    }, 1000); // Simulation d'1 seconde de chargement
  }
}
