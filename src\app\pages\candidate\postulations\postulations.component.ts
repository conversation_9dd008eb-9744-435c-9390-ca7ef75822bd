import { Component, OnInit } from '@angular/core';
import { PostulationService, Postulation } from '../../../services/postulation.service';
import { AuthService } from '../../../services/auth.service';
import { AlertsService } from '../../../services/alerts.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-postulations',
  templateUrl: './postulations.component.html',
  styleUrls: ['./postulations.component.scss']
})
export class PostulationsComponent implements OnInit {
  postulations: Postulation[] = [];
  isLoading = false;
  displayedColumns: string[] = [
    'offre',
    'datePostulation',
    'scoreCv',
    'decisionCv',
    'scoreEntretien',
    'decisionEntretien',
    'statut',
    'actions'
  ];

  constructor(
    private postulationService: PostulationService,
    private authService: AuthService,
    private alertsService: AlertsService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadPostulations();
  }

  loadPostulations(): void {
    this.isLoading = true;
    const candidatId = this.authService.getCurrentUserId();
    
    if (candidatId) {
      this.postulationService.getPostulationsByCandidat(candidatId).subscribe({
        next: (postulations) => {
          this.postulations = postulations;
          this.isLoading = false;
        },
        error: (error) => {
          this.alertsService.showError('Erreur lors du chargement des postulations');
          this.isLoading = false;
        }
      });
    } else {
      this.alertsService.showError('ID candidat non trouvé');
      this.isLoading = false;
    }
  }

  getDecisionColor(decision: string): string {
    switch (decision) {
      case 'ACCEPTE':
        return 'accent';
      case 'REFUSE':
        return 'warn';
      default:
        return 'primary';
    }
  }

  getDecisionLabel(decision: string): string {
    switch (decision) {
      case 'ACCEPTE':
        return 'Accepté';
      case 'REFUSE':
        return 'Refusé';
      case 'EN_ATTENTE':
        return 'En attente';
      default:
        return decision;
    }
  }

  getStatutLabel(statut: string): string {
    switch (statut) {
      case 'EN_COURS':
        return 'En cours';
      case 'ACCEPTEE':
        return 'Acceptée';
      case 'REFUSEE':
        return 'Refusée';
      default:
        return statut;
    }
  }

  viewOffreDetails(offreId: number): void {
    this.router.navigate(['/offres', offreId]);
  }

  startEntretien(postulation: Postulation): void {
    if (postulation.decisionCv === 'ACCEPTE' && postulation.entretienId) {
      this.postulationService.getEntretienLink(postulation.id).subscribe({
        next: (response) => {
          window.open(response.lienEntretien, '_blank');
        },
        error: (error) => {
          this.alertsService.showError('Erreur lors de la récupération du lien d\'entretien');
        }
      });
    }
  }

  cancelPostulation(postulation: Postulation): void {
    this.alertsService.showConfirm(
      'Confirmation',
      'Êtes-vous sûr de vouloir annuler cette candidature ?',
      'Annuler',
      'Retour'
    ).then((confirmed) => {
      if (confirmed) {
        this.postulationService.cancelPostulation(postulation.id).subscribe({
          next: () => {
            this.alertsService.showSuccess('Candidature annulée avec succès');
            this.loadPostulations();
          },
          error: (error) => {
            this.alertsService.showError('Erreur lors de l\'annulation de la candidature');
          }
        });
      }
    });
  }

  confirmDisponibilite(postulation: Postulation, disponible: boolean): void {
    this.postulationService.confirmDisponibilite(postulation.id, disponible).subscribe({
      next: (updatedPostulation) => {
        const message = disponible 
          ? 'Disponibilité confirmée avec succès'
          : 'Indisponibilité confirmée';
        this.alertsService.showSuccess(message);
        this.loadPostulations();
      },
      error: (error) => {
        this.alertsService.showError('Erreur lors de la confirmation de disponibilité');
      }
    });
  }
} 